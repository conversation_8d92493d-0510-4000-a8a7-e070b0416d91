package com.nacos.service.adapter;

import com.nacos.entity.po.ProviderCapabilityPO;
import com.nacos.model.SoundView.SoundViewApiUtil;
import com.nacos.model.SoundView.model.response.LanguageListResponseBO;
import com.nacos.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * LINGYANG语种适配器
 * 负责调用LINGYANG API获取语种列表并转换为标准格式
 * 
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LingyangLanguageAdapter {

    private static final String PROVIDER_CODE = "LINGYANG";

    /**
     * 获取LINGYANG支持的语种列表
     * 
     * @return 语种列表结果
     */
    public Result<List<LanguageListResponseBO.LanguageInfo>> fetchSupportedLanguages() {
        String methodName = "fetchSupportedLanguages";
        log.info("[{}] 开始获取LINGYANG支持的语种列表", methodName);

        try {
            // 调用现有的SoundViewApiUtil获取语种列表
            Result<List<LanguageListResponseBO.LanguageInfo>> result =
                SoundViewApiUtil.getLanguageListWithResult();

            if (!result.isSuccess() || result.getData() == null) {
                log.error("[{}] 获取LINGYANG语种列表失败: {}", methodName, result.getMessage());
                return Result.ERROR("获取LINGYANG语种列表失败: " + result.getMessage());
            }

            List<LanguageListResponseBO.LanguageInfo> languages = result.getData();
            log.info("[{}] 成功获取LINGYANG语种列表，共{}种语言", methodName, languages.size());

            // 过滤支持完整视频翻译功能的语种
            List<LanguageListResponseBO.LanguageInfo> supportedLanguages =
                languages.stream()
                    .filter(this::isLanguageSupportedForVideoTranslation)
                    .toList();

            log.info("[{}] 过滤后支持视频翻译的语种数量: {}", methodName, supportedLanguages.size());
            return Result.SUCCESS(supportedLanguages);

        } catch (Exception e) {
            log.error("[{}] 获取LINGYANG语种列表异常", methodName, e);
            return Result.ERROR("获取LINGYANG语种列表异常: " + e.getMessage());
        }
    }

    /**
     * 将LINGYANG语种列表转换为ProviderCapabilityPO格式
     * 
     * @param languages LINGYANG语种列表
     * @return 转换后的ProviderCapabilityPO列表
     */
    public List<ProviderCapabilityPO> convertToProviderCapability(
            List<LanguageListResponseBO.LanguageInfo> languages) {
        String methodName = "convertToProviderCapability";
        log.info("[{}] 开始转换LINGYANG语种数据为ProviderCapabilityPO格式", methodName);

        if (languages == null || languages.isEmpty()) {
            log.warn("[{}] 输入的语种列表为空", methodName);
            return new ArrayList<>();
        }

        List<ProviderCapabilityPO> result = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 生成所有可能的语言对组合
        for (LanguageListResponseBO.LanguageInfo sourceLanguage : languages) {
            for (LanguageListResponseBO.LanguageInfo targetLanguage : languages) {
                // 跳过相同语言的翻译
                if (sourceLanguage.getCode().equals(targetLanguage.getCode())) {
                    continue;
                }

                // 验证语言代码
                if (!isValidLanguageCode(sourceLanguage.getCode()) || 
                    !isValidLanguageCode(targetLanguage.getCode())) {
                    log.warn("[{}] 跳过无效的语言代码: {} -> {}", 
                            methodName, sourceLanguage.getCode(), targetLanguage.getCode());
                    continue;
                }

                ProviderCapabilityPO capability = createProviderCapability(
                    sourceLanguage, targetLanguage, now);
                result.add(capability);
            }
        }

        log.info("[{}] 转换完成，生成{}个语言对配置", methodName, result.size());
        return result;
    }

    /**
     * 创建ProviderCapabilityPO对象
     * 
     * @param sourceLanguage 源语言
     * @param targetLanguage 目标语言
     * @param now 当前时间
     * @return ProviderCapabilityPO对象
     */
    private ProviderCapabilityPO createProviderCapability(
            LanguageListResponseBO.LanguageInfo sourceLanguage,
            LanguageListResponseBO.LanguageInfo targetLanguage,
            LocalDateTime now) {

        ProviderCapabilityPO capability = new ProviderCapabilityPO();
        
        // 基本信息
        capability.setProviderCode(PROVIDER_CODE);
        capability.setSourceLanguage(sourceLanguage.getCode());
        capability.setTargetLanguage(targetLanguage.getCode());
        
        // LINGYANG平台的语言代码映射（目前与标准代码相同）
        capability.setProviderSourceCode(sourceLanguage.getCode());
        capability.setProviderTargetCode(targetLanguage.getCode());
        
        // 状态和时间
        capability.setEnabled(1);
        capability.setCreatedTime(now);
        capability.setUpdatedTime(now);

        return capability;
    }

    /**
     * 检查语种是否支持视频翻译
     * 
     * @param language 语种信息
     * @return 是否支持
     */
    private boolean isLanguageSupportedForVideoTranslation(
            LanguageListResponseBO.LanguageInfo language) {

        if (language == null || !StringUtils.hasText(language.getCode())) {
            return false;
        }

        // 由于新的API响应结构简化，假设所有返回的语种都支持视频翻译
        // 可以根据实际需要添加更多的过滤条件
        return true;
    }

    /**
     * 验证语言代码是否有效
     * 
     * @param languageCode 语言代码
     * @return 是否有效
     */
    private boolean isValidLanguageCode(String languageCode) {
        if (!StringUtils.hasText(languageCode)) {
            return false;
        }

        // 基本格式验证：2-5个字符，只包含字母、数字和连字符
        return languageCode.matches("^[a-zA-Z0-9-]{2,5}$");
    }

    /**
     * 获取语种数据转换统计信息
     * 
     * @param originalLanguages 原始语种列表
     * @param convertedCapabilities 转换后的能力列表
     * @return 统计信息
     */
    public String getConversionStatistics(
            List<LanguageListResponseBO.LanguageInfo> originalLanguages,
            List<ProviderCapabilityPO> convertedCapabilities) {
        
        if (originalLanguages == null || convertedCapabilities == null) {
            return "统计信息不可用";
        }

        int originalCount = originalLanguages.size();
        int supportedCount = (int) originalLanguages.stream()
            .filter(this::isLanguageSupportedForVideoTranslation)
            .count();
        int pairCount = convertedCapabilities.size();

        return String.format("原始语种: %d个, 支持视频翻译: %d个, 生成语言对: %d个", 
                            originalCount, supportedCount, pairCount);
    }

    /**
     * 验证转换结果
     * 
     * @param capabilities 转换后的能力列表
     * @return 验证结果
     */
    public Result<String> validateConversionResult(List<ProviderCapabilityPO> capabilities) {
        String methodName = "validateConversionResult";
        
        if (capabilities == null || capabilities.isEmpty()) {
            return Result.ERROR("转换结果为空");
        }

        // 检查必要字段
        for (ProviderCapabilityPO capability : capabilities) {
            if (!StringUtils.hasText(capability.getProviderCode()) ||
                !StringUtils.hasText(capability.getSourceLanguage()) ||
                !StringUtils.hasText(capability.getTargetLanguage())) {
                
                log.error("[{}] 发现无效的转换结果: {}", methodName, capability);
                return Result.ERROR("转换结果包含无效数据");
            }
        }

        log.info("[{}] 转换结果验证通过，共{}条记录", methodName, capabilities.size());
        return Result.SUCCESS("验证通过");
    }
}
